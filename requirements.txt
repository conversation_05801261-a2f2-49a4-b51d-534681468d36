absl-py==2.2.2
accelerate==1.7.0
adagio==0.2.6
aiohappyeyeballs==2.6.1
aiohttp==3.12.0
aiohttp-cors==0.8.1
aiosignal==1.3.2
ale-py==0.11.1
alembic==1.16.1
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
appdirs==1.4.4
asttokens @ file:///home/<USER>/feedstock_root/build_artifacts/asttokens_1733250440834/work
attrs==25.3.0
autogluon==1.3.1
autogluon.common==1.3.1
autogluon.core==1.3.1
autogluon.features==1.3.1
autogluon.multimodal==1.3.1
autogluon.tabular==1.3.1
autogluon.timeseries==1.3.1
beartype==0.21.0
beautifulsoup4==4.13.4
blis==1.3.0
boto3==1.38.23
botocore==1.38.23
cachetools==5.5.2
catalogue==2.0.10
catboost==1.2.8
certifi==2025.4.26
charset-normalizer==3.4.2
click==8.1.8
cloudpathlib==0.21.1
cloudpickle==3.1.1
colorama==0.4.6
colorful==0.5.6
colorlog==6.9.0
comm @ file:///home/<USER>/feedstock_root/build_artifacts/comm_1733502965406/work
confection==0.1.5
contourpy==1.3.2
coreforecast==0.0.15
cycler==0.12.1
cymem==2.0.11
dataclasses-json==0.6.7
datasets==2.14.4
debugpy @ file:///croot/debugpy_1736267418885/work
decorator @ file:///home/<USER>/feedstock_root/build_artifacts/decorator_1740384970518/work
defusedxml==0.7.1
dill==0.3.7
distlib==0.3.9
distro==1.9.0
einops==0.8.1
et_xmlfile==2.0.0
evaluate==0.4.3
exceptiongroup @ file:///home/<USER>/feedstock_root/build_artifacts/exceptiongroup_1746947292760/work
executing @ file:///home/<USER>/feedstock_root/build_artifacts/executing_1745502089858/work
Farama-Notifications==0.0.4
fastai==2.8.2
fastcore==1.8.2
fastdownload==0.0.7
fastprogress==1.0.3
fasttransform==0.0.2
filelock==3.18.0
fonttools==4.58.0
frozenlist==1.6.0
fs==2.4.16
fsspec==2025.5.1
fugue==0.9.1
future==1.0.0
gdown==5.2.0
gluonts==0.16.1
google-api-core==2.24.2
google-auth==2.40.2
googleapis-common-protos==1.70.0
graphviz==0.20.3
greenlet==3.2.2
grpcio==1.71.0
gym==0.26.2
gym-notices==0.0.8
gymnasium==1.1.1
h11==0.16.0
hf-xet==1.1.2
httpcore==1.0.9
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.32.0
hyperopt==0.2.7
idna==3.10
imageio==2.37.0
importlib_metadata @ file:///home/<USER>/feedstock_root/build_artifacts/bld/rattler-build_importlib-metadata_1747934053/work
ipykernel @ file:///home/<USER>/feedstock_root/build_artifacts/ipykernel_1719845459717/work
ipython @ file:///home/<USER>/feedstock_root/build_artifacts/bld/rattler-build_ipython_1745672166/work
ipython_pygments_lexers @ file:///home/<USER>/feedstock_root/build_artifacts/ipython_pygments_lexers_1737123620466/work
jedi @ file:///home/<USER>/feedstock_root/build_artifacts/jedi_1733300866624/work
Jinja2==3.1.6
jiter==0.10.0
jmespath==1.0.1
joblib==1.5.1
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
jupyter_client @ file:///home/<USER>/feedstock_root/build_artifacts/jupyter_client_1733440914442/work
jupyter_core @ file:///home/<USER>/feedstock_root/build_artifacts/jupyter_core_1727163409502/work
kiwisolver==1.4.8
langchain==0.3.25
langchain-community==0.3.25
langchain-core==0.3.65
langchain-text-splitters==0.3.8
langcodes==3.5.0
langsmith==0.3.45
language_data==1.3.0
lazy_loader==0.4
lightgbm==4.6.0
lightning==2.5.1.post0
lightning-utilities==0.14.3
llvmlite==0.44.0
Mako==1.3.10
marisa-trie==1.2.1
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.3
matplotlib-inline @ file:///home/<USER>/feedstock_root/build_artifacts/matplotlib-inline_1733416936468/work
mdurl==0.1.2
mlforecast==0.13.6
model-index==0.1.11
mpmath==1.3.0
msgpack==1.1.0
multidict==6.4.4
multiprocess==0.70.15
murmurhash==1.0.13
mypy_extensions==1.1.0
narwhals==1.40.0
nest_asyncio @ file:///home/<USER>/feedstock_root/build_artifacts/nest-asyncio_1733325553580/work
networkx==3.4.2
nlpaug==1.1.11
nltk==3.8.1
numba==0.61.2
numpy==2.1.3
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-cusparselt-cu12==0.6.2
nvidia-ml-py3==7.352.0
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
omegaconf==2.3.0
openai==1.88.0
opencensus==0.11.4
opencensus-context==0.1.3
opencv-python==*********
opendatalab==0.0.10
openmim==0.3.9
openpyxl==3.1.5
openxlab==0.0.11
optuna==4.3.0
ordered-set==4.1.0
orjson==3.10.18
packaging==24.2
pandas==2.2.3
parso @ file:///home/<USER>/feedstock_root/build_artifacts/parso_1733271261340/work
patsy==1.0.1
pdf2image==1.17.0
pexpect @ file:///home/<USER>/feedstock_root/build_artifacts/pexpect_1733301927746/work
pickleshare @ file:///home/<USER>/feedstock_root/build_artifacts/pickleshare_1733327343728/work
pillow==11.2.1
platformdirs @ file:///home/<USER>/feedstock_root/build_artifacts/bld/rattler-build_platformdirs_1746710438/work
plotly==6.1.1
plum-dispatch==2.5.7
preshed==3.0.9
prometheus_client==0.22.0
prompt_toolkit @ file:///home/<USER>/feedstock_root/build_artifacts/prompt-toolkit_1744724089886/work
propcache==0.3.1
proto-plus==1.26.1
protobuf==6.31.0
psutil @ file:///croot/psutil_1736367091698/work
ptyprocess @ file:///home/<USER>/feedstock_root/build_artifacts/ptyprocess_1733302279685/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl#sha256=92c32ff62b5fd8cf325bec5ab90d7be3d2a8ca8c8a3813ff487a8d2002630d1f
pure_eval @ file:///home/<USER>/feedstock_root/build_artifacts/pure_eval_1733569405015/work
py-spy==0.4.0
py4j==0.10.9.9
pyarrow==20.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycryptodome==3.23.0
pydantic==2.11.5
pydantic-settings==2.9.1
pydantic_core==2.33.2
pygame==2.6.1
Pygments @ file:///home/<USER>/feedstock_root/build_artifacts/pygments_1736243443484/work
pyparsing==3.2.3
PySocks==1.7.1
pytesseract==0.3.13
python-dateutil @ file:///home/<USER>/feedstock_root/build_artifacts/python-dateutil_1733215673016/work
python-dotenv==1.1.0
pytorch-lightning==2.5.1.post0
pytorch-metric-learning==2.8.1
pytz==2025.2
PyYAML==6.0.2
pyzmq @ file:///croot/pyzmq_1734687138743/work
ray==2.44.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==14.0.0
rpds-py==0.25.1
rsa==4.9.1
s3transfer==0.13.0
safetensors==0.5.3
scikit-image==0.25.2
scikit-learn==1.6.1
scipy==1.15.3
seaborn==0.13.2
sentencepiece==0.2.0
seqeval==1.2.2
shellingham==1.5.4
six @ file:///home/<USER>/feedstock_root/build_artifacts/six_1733380938961/work
smart-open==7.1.0
sniffio==1.3.1
soupsieve==2.7
spacy==3.8.7
spacy-legacy==3.0.12
spacy-loggers==1.0.5
SQLAlchemy==2.0.41
srsly==2.5.1
stable_baselines3==2.6.0
stack_data @ file:///home/<USER>/feedstock_root/build_artifacts/stack_data_1733569443808/work
statsforecast==2.0.1
statsmodels==0.14.4
sympy==1.13.1
tabulate==0.9.0
tenacity==9.1.2
tensorboard==2.19.0
tensorboard-data-server==0.7.2
tensorboardX==*******
text-unidecode==1.3
thinc==8.3.6
threadpoolctl==3.6.0
tifffile==2025.5.24
timm==1.0.3
tokenizers==0.21.1
tools==1.0.2
toolz==0.12.1
torch==2.6.0
torchmetrics==1.7.1
torchvision==0.21.0
tornado @ file:///croot/tornado_1747918059467/work
tqdm==4.67.1
traitlets @ file:///home/<USER>/feedstock_root/build_artifacts/traitlets_1733367359838/work
transformers==4.49.0
triad==0.9.8
triton==3.2.0
typer==0.15.4
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions @ file:///home/<USER>/feedstock_root/build_artifacts/bld/rattler-build_typing_extensions_1744302253/work
tzdata==2025.2
urllib3==2.4.0
utilsforecast==0.2.10
virtualenv==20.31.2
wasabi==1.1.3
wcwidth @ file:///home/<USER>/feedstock_root/build_artifacts/wcwidth_1733231326287/work
weasel==0.4.1
Werkzeug==3.1.3
window_ops==0.0.15
wrapt==1.17.2
xgboost==3.0.2
xxhash==3.5.0
yarl==1.20.0
zipp @ file:///home/<USER>/feedstock_root/build_artifacts/zipp_1732827521216/work
zstandard==0.23.0
