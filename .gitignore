# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.venv/
venv/
ENV/
env/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# PyCharm
.idea/

# VSCode
.vscode/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Local config files
*.env
*.local
*.secret

# Data files
*.csv
*.tsv
*.xls
*.xlsx
*.h5
*.db
*.sqlite
*.pkl
*.joblib

# Logs
*.log

# OS files
.DS_Store
Thumbs.db
